# Apache SSL框架网站搭建脚本

本文档介绍了两个用于搭建Apache框架网站与SSL证书的脚本。

## 脚本概述

### 1. centos_server03_ssl_web_config.sh
专门为CentOS Server03 (WEB服务器2) 设计的SSL配置脚本，与现有的CentOS服务器架构完全兼容。

**功能特性：**
- 自动配置Apache + SSL
- 生成自签名SSL证书
- HTTP到HTTPS自动重定向
- 安全头部配置
- 防火墙和SELinux配置
- 简单静态网页（无CSS样式）

**固定配置：**
- IP地址: *************
- 域名: www.nuli.cn
- 网站目录: /var/www/yuanchu
- 端口: 443 (HTTPS), 80 (重定向)

### 2. apache_ssl_framework_setup.sh
通用的Apache SSL框架搭建脚本，支持自定义配置参数。

**功能特性：**
- 支持CentOS/RHEL和Debian/Ubuntu系统
- 可配置域名、IP地址、网站目录
- 自动检测系统类型
- 模块化配置
- 命令行参数支持

## 使用方法

### CentOS Server03专用脚本

```bash
# 1. 赋予执行权限
chmod +x centos_server03_ssl_web_config.sh

# 2. 以root用户运行
sudo ./centos_server03_ssl_web_config.sh
```

### 通用Apache SSL框架脚本

```bash
# 1. 赋予执行权限
chmod +x apache_ssl_framework_setup.sh

# 2. 查看帮助信息
./apache_ssl_framework_setup.sh --help

# 3. 使用默认配置
sudo ./apache_ssl_framework_setup.sh

# 4. 自定义配置示例
sudo ./apache_ssl_framework_setup.sh -d nuli.cn -i *************
sudo ./apache_ssl_framework_setup.sh --domain example.com --ip ********** --webroot /var/www/mysite
```

## 配置参数说明

### 通用脚本参数

| 参数 | 长参数 | 说明 | 默认值 |
|------|--------|------|--------|
| -d | --domain | 设置域名 | example.com |
| -i | --ip | 设置IP地址 | ************* |
| -w | --webroot | 设置网站根目录 | /var/www/html |
| -c | --country | SSL证书国家代码 | CN |
| -s | --state | SSL证书省份 | Beijing |
| -l | --city | SSL证书城市 | Beijing |
| -o | --org | SSL证书组织 | Example Organization |
| -u | --unit | SSL证书部门 | IT Department |
| -h | --help | 显示帮助信息 | - |

## 安全配置

### SSL安全特性
- TLS 1.2+ 协议支持
- 强加密套件配置
- 禁用不安全的SSL/TLS版本
- 自签名证书生成

### 安全头部
- Strict-Transport-Security (HSTS)
- X-Frame-Options: DENY
- X-Content-Type-Options: nosniff
- X-XSS-Protection: 1; mode=block
- Referrer-Policy: strict-origin-when-cross-origin

### 防火墙配置
- 自动配置HTTP (80) 和HTTPS (443) 端口
- 支持firewalld (CentOS/RHEL) 和ufw (Ubuntu/Debian)

## 系统要求

### 支持的操作系统
- CentOS 9
- RHEL 9
- Ubuntu 20.04+
- Debian 11+

### 必需的软件包
脚本会自动安装以下软件包：
- httpd/apache2
- mod_ssl
- openssl
- curl (用于测试)

## 文件结构

### CentOS Server03配置后的文件结构
```
/var/www/yuanchu/           # 网站根目录
├── index.html              # 主页文件

/etc/httpd/conf.d/          # Apache配置目录
├── ssl-redirect.conf       # HTTP重定向配置
└── ssl-website.conf        # HTTPS虚拟主机配置

/etc/pki/tls/               # SSL证书目录
├── certs/www.nuli.cn.crt   # SSL证书
└── private/www.nuli.cn.key # SSL私钥
```

### 通用脚本配置后的文件结构
```
[WEBROOT]/                  # 自定义网站根目录
├── index.html              # 主页文件

[APACHE_CONFIG_DIR]/        # Apache配置目录
├── ssl-redirect.conf       # HTTP重定向配置
└── ssl-website.conf        # HTTPS虚拟主机配置

[SSL_CERT_DIR]/             # SSL证书目录
├── [DOMAIN].crt            # SSL证书
└── [SSL_KEY_DIR]/[DOMAIN].key # SSL私钥
```

## 测试验证

### 自动测试
脚本执行完成后会自动进行以下测试：
- HTTP重定向测试
- HTTPS访问测试
- SSL证书验证
- 端口监听检查
- SSL模块加载检查

### 手动测试命令
```bash
# 测试HTTP重定向
curl -I http://your-domain.com/

# 测试HTTPS访问
curl -k -I https://your-domain.com/

# 测试SSL证书
openssl s_client -connect your-domain.com:443 -servername your-domain.com

# 检查端口监听
ss -tulnp | grep -E ":80|:443"

# 检查Apache状态
systemctl status httpd  # CentOS/RHEL
systemctl status apache2  # Ubuntu/Debian
```

## 故障排除

### 常见问题

1. **Apache启动失败**
   ```bash
   # 检查配置语法
   httpd -t  # CentOS/RHEL
   apache2ctl -t  # Ubuntu/Debian
   
   # 查看错误日志
   journalctl -u httpd -f  # CentOS/RHEL
   journalctl -u apache2 -f  # Ubuntu/Debian
   ```

2. **SSL证书问题**
   ```bash
   # 验证证书文件
   openssl x509 -in /path/to/certificate.crt -text -noout
   
   # 检查私钥
   openssl rsa -in /path/to/private.key -check
   ```

3. **防火墙问题**
   ```bash
   # CentOS/RHEL
   firewall-cmd --list-all
   firewall-cmd --permanent --add-service=https
   firewall-cmd --reload
   
   # Ubuntu/Debian
   ufw status
   ufw allow 443/tcp
   ```

4. **SELinux问题** (仅CentOS/RHEL)
   ```bash
   # 检查SELinux状态
   getenforce
   
   # 查看SELinux日志
   ausearch -m avc -ts recent
   
   # 设置正确的上下文
   restorecon -R /var/www/
   ```

## 与现有架构的集成

这些脚本与现有的CentOS配置架构完全兼容：

- **DNS服务器** (Server01): 192.168.88.10
- **WEB服务器1** (Server02): 192.168.88.20 (HTTP + WordPress)
- **WEB服务器2** (Server03): ************* (HTTPS + SSL) ← 新增
- **客户端配置**: 自动DNS解析和访问测试

## 注意事项

1. **自签名证书**: 浏览器会显示安全警告，这是正常现象
2. **生产环境**: 建议使用CA签发的正式SSL证书
3. **备份**: 脚本会自动备份原始配置文件
4. **权限**: 必须以root用户运行脚本
5. **网络**: 确保防火墙允许HTTP和HTTPS流量

## 技术支持

如需技术支持或遇到问题，请检查：
1. 系统日志: `journalctl -f`
2. Apache错误日志: `/var/log/httpd/` 或 `/var/log/apache2/`
3. SSL证书有效性
4. 防火墙和SELinux配置
