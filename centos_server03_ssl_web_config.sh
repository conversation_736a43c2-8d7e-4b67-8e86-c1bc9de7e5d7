#!/bin/bash
# CentOS Server03 - WEB服务器2配置脚本
# 系统: CentOS 9
# 功能: Apache + SSL证书，端口443 (HTTPS)

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 全局变量
SERVER_IP="*************"
SUBNET_MASK="24"
DOMAIN="nuli.cn"
SSL_DOMAIN="www.$DOMAIN"

# 打印带颜色的信息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[HEADER]${NC} $1"
}

# 检查是否以root用户运行
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "请以root用户运行此脚本"
        exit 1
    fi
}

# 设置固定的服务IP配置
show_config() {
    print_header "CentOS WEB服务器2固定IP配置："
    print_info "WEB服务器2 IP: $SERVER_IP/$SUBNET_MASK"
    print_info "SSL域名: $SSL_DOMAIN"
    print_info "服务: Apache + SSL (HTTPS)"
    print_info "端口: 443 (HTTPS), 80 (重定向到HTTPS)"
    echo ""

    read -p "确认使用以上固定配置? (y/n): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        print_error "用户取消配置"
        exit 1
    fi
}

# 添加服务IP地址
setup_ip() {
    print_header "配置网络IP地址..."

    # 获取网络接口名称
    INTERFACE=$(ip -o -4 route show to default | awk '{print $5}')

    if [ -z "$INTERFACE" ]; then
        print_error "无法确定网络接口"
        exit 1
    fi

    print_info "在网络接口 $INTERFACE 上添加服务IP地址..."

    # 检查IP是否已经存在
    if ip addr show $INTERFACE | grep -q "$SERVER_IP"; then
        print_warning "IP地址 $SERVER_IP 已经存在于接口 $INTERFACE 上"
        return 0
    fi

    # 添加IP地址到现有接口（不删除原有配置）
    if ip addr add $SERVER_IP/$SUBNET_MASK dev $INTERFACE 2>/dev/null; then
        print_success "成功添加IP地址 $SERVER_IP 到接口 $INTERFACE"
    else
        # 如果添加失败，可能是因为IP已存在，检查是否真的存在
        if ip addr show $INTERFACE | grep -q "$SERVER_IP"; then
            print_warning "IP地址 $SERVER_IP 已经存在，跳过添加"
        else
            print_error "添加IP地址失败"
            exit 1
        fi
    fi

    # 创建持久化配置（CentOS方式）
    print_info "创建持久化IP配置..."

    # 创建网络脚本来在启动时添加IP
    cat > /etc/systemd/system/web2-service-ip.service << EOF
[Unit]
Description=Add Web2 Service IP Address
After=network.target

[Service]
Type=oneshot
ExecStart=/bin/ip addr add $SERVER_IP/$SUBNET_MASK dev $INTERFACE
RemainAfterExit=yes
StandardOutput=journal

[Install]
WantedBy=multi-user.target
EOF

    # 启用服务
    systemctl enable web2-service-ip.service

    # 验证IP配置
    sleep 2
    print_info "当前接口 $INTERFACE 的IP配置："
    ip addr show $INTERFACE | grep inet

    print_success "服务IP地址添加完成，已配置开机自启"
}

# 配置CentOS软件源
configure_centos_repos() {
    print_header "配置CentOS软件源..."

    # 备份原始repo文件
    if [ ! -d "/etc/yum.repos.d.backup" ]; then
        cp -r /etc/yum.repos.d /etc/yum.repos.d.backup 2>/dev/null || true
        print_info "已备份原始软件源配置"
    fi

    # 配置EPEL仓库
    print_info "配置EPEL仓库..."
    if ! dnf list installed epel-release >/dev/null 2>&1; then
        dnf install -y epel-release || print_warning "EPEL仓库安装失败"
    fi

    # 清理和重建缓存
    dnf clean all
    dnf makecache

    print_success "CentOS软件源配置完成"
}

# 安装Apache和SSL模块
install_apache_ssl() {
    print_header "安装Apache和SSL模块..."

    # 更新系统
    dnf update -y

    # 安装Apache和SSL模块 (CentOS方式)
    if ! dnf install -y httpd mod_ssl openssl; then
        print_error "Apache和SSL模块安装失败，请检查网络连接或软件源配置"
        exit 1
    fi

    print_success "Apache和SSL模块安装完成"
}

# 生成SSL证书
generate_ssl_certificate() {
    print_header "生成SSL证书..."

    # 检查openssl是否可用
    if ! command -v openssl >/dev/null 2>&1; then
        print_error "openssl未安装，请先安装openssl"
        exit 1
    fi

    # 创建SSL证书目录
    mkdir -p /etc/pki/tls/certs
    mkdir -p /etc/pki/tls/private

    # 检查SSL证书是否已存在
    if [ -f "/etc/pki/tls/certs/$SSL_DOMAIN.crt" ] && [ -s "/etc/pki/tls/certs/$SSL_DOMAIN.crt" ]; then
        print_info "SSL证书已存在，跳过生成"
        return 0
    fi

    print_info "生成SSL私钥..."
    openssl genrsa -out /etc/pki/tls/private/$SSL_DOMAIN.key 2048

    print_info "生成证书签名请求..."
    openssl req -new -key /etc/pki/tls/private/$SSL_DOMAIN.key -out /tmp/$SSL_DOMAIN.csr -subj "/C=CN/ST=Beijing/L=Beijing/O=Nuli Organization/OU=IT Department/CN=$SSL_DOMAIN/emailAddress=admin@$DOMAIN"

    print_info "生成自签名证书..."
    openssl x509 -req -days 365 -in /tmp/$SSL_DOMAIN.csr -signkey /etc/pki/tls/private/$SSL_DOMAIN.key -out /etc/pki/tls/certs/$SSL_DOMAIN.crt

    # 设置权限
    chmod 600 /etc/pki/tls/private/$SSL_DOMAIN.key
    chmod 644 /etc/pki/tls/certs/$SSL_DOMAIN.crt

    # 清理临时文件
    rm -f /tmp/$SSL_DOMAIN.csr

    # 验证证书
    if openssl x509 -in /etc/pki/tls/certs/$SSL_DOMAIN.crt -text -noout > /dev/null 2>&1; then
        print_success "SSL证书生成并验证成功"
    else
        print_error "SSL证书验证失败"
        exit 1
    fi
}

# 配置Apache虚拟主机
configure_apache_vhosts() {
    print_header "配置Apache虚拟主机..."

    # 检查httpd是否可用
    if ! command -v httpd >/dev/null 2>&1; then
        print_error "httpd未安装，请先安装httpd"
        exit 1
    fi

    # 创建网站目录
    mkdir -p /var/www/yuanchu

    # 配置HTTP虚拟主机（重定向到HTTPS）
    cat > /etc/httpd/conf.d/ssl-redirect.conf << EOF
<VirtualHost *:80>
    ServerName $SSL_DOMAIN
    ServerAlias $SERVER_IP
    ServerAlias localhost

    # 重定向所有HTTP请求到HTTPS
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [R=301,L]

    ErrorLog /var/log/httpd/ssl-redirect-error.log
    CustomLog /var/log/httpd/ssl-redirect-access.log combined
</VirtualHost>
EOF

    # 配置HTTPS虚拟主机
    cat > /etc/httpd/conf.d/ssl-website.conf << EOF
<VirtualHost *:443>
    ServerName $SSL_DOMAIN
    ServerAlias $SERVER_IP
    DocumentRoot /var/www/yuanchu
    DirectoryIndex index.html

    # SSL配置
    SSLEngine on
    SSLCertificateFile /etc/pki/tls/certs/$SSL_DOMAIN.crt
    SSLCertificateKeyFile /etc/pki/tls/private/$SSL_DOMAIN.key

    # SSL安全配置
    SSLProtocol all -SSLv2 -SSLv3 -TLSv1 -TLSv1.1
    SSLCipherSuite ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384
    SSLHonorCipherOrder on

    # 安全头部
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    Header always set X-Frame-Options DENY
    Header always set X-Content-Type-Options nosniff
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"

    <Directory /var/www/yuanchu>
        AllowOverride All
        Require all granted
        Options Indexes FollowSymLinks
    </Directory>

    ErrorLog /var/log/httpd/ssl-website-error.log
    CustomLog /var/log/httpd/ssl-website-access.log combined
</VirtualHost>
EOF

    print_success "Apache虚拟主机配置完成"
}

# 创建静态网页
create_static_website() {
    print_header "创建静态网页..."

    # 创建简单的静态主页（符合用户偏好：简单、无CSS样式）
    cat > /var/www/yuanchu/index.html << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CentOS Server03</title>
</head>
<body>
    <h1>CentOS Server03</h1>

    <h2>服务器配置信息</h2>
    <p>操作系统: CentOS 9</p>
    <p>IP地址: *************</p>
    <p>域名: www.nuli.cn</p>
    <p>服务: Apache + SSL</p>
    <p>端口: 443 (HTTPS)</p>
    <p>状态: 正常运行</p>

    <h2>技术特性</h2>
    <p>Web服务器: Apache</p>
    <p>SSL/TLS: 自签名证书</p>
    <p>安全协议: TLS 1.2+</p>
    <p>字符编码: UTF-8</p>
    <p>SELinux: 兼容</p>
    <p>防火墙: 已配置</p>

    <h2>安全配置</h2>
    <p>HTTP重定向: 自动跳转HTTPS</p>
    <p>HSTS: 已启用</p>
    <p>X-Frame-Options: DENY</p>
    <p>X-Content-Type-Options: nosniff</p>
    <p>X-XSS-Protection: 已启用</p>

</body>
</html>
EOF

    # 设置权限
    chown -R apache:apache /var/www/yuanchu
    chmod -R 755 /var/www/yuanchu

    print_success "静态网页创建完成"
}

# 配置防火墙
configure_firewall() {
    print_header "配置CentOS防火墙..."

    # 检查firewalld状态
    if ! systemctl is-active --quiet firewalld; then
        systemctl enable firewalld
        systemctl start firewalld
        print_info "已启动firewalld服务"
    fi

    # 添加HTTP和HTTPS服务到防火墙
    firewall-cmd --permanent --add-service=http
    firewall-cmd --permanent --add-service=https
    firewall-cmd --permanent --add-port=80/tcp
    firewall-cmd --permanent --add-port=443/tcp

    # 重新加载防火墙配置
    firewall-cmd --reload

    print_success "防火墙配置完成"
}

# 配置SELinux
configure_selinux() {
    print_header "配置SELinux..."

    # 检查SELinux状态
    if command -v getenforce >/dev/null 2>&1; then
        SELINUX_STATUS=$(getenforce)
        print_info "当前SELinux状态: $SELINUX_STATUS"

        if [ "$SELINUX_STATUS" = "Enforcing" ]; then
            print_info "配置SELinux策略以支持Apache和SSL..."

            # 设置Apache相关的SELinux上下文
            setsebool -P httpd_can_network_connect on
            restorecon -R /var/www/yuanchu/
            restorecon -R /etc/pki/tls/

            print_success "SELinux配置完成"
        fi
    else
        print_info "SELinux未安装或不可用"
    fi
}

# 启动Apache服务
start_apache() {
    print_header "启动Apache服务..."

    # 检查systemctl是否可用
    if ! command -v systemctl >/dev/null 2>&1; then
        print_error "systemctl不可用，请检查系统服务管理器"
        exit 1
    fi

    # 启动服务 (CentOS方式)
    systemctl enable httpd
    systemctl restart httpd

    # 检查服务状态
    if systemctl is-active --quiet httpd; then
        print_success "Apache服务启动成功"
    else
        print_error "Apache服务启动失败"
        journalctl -u httpd --no-pager -l
        exit 1
    fi
}

# 测试SSL服务
test_ssl_service() {
    print_header "测试SSL服务..."

    # 等待服务完全启动
    sleep 3

    # 测试HTTP重定向
    print_info "测试HTTP重定向..."
    if curl -s -I http://localhost/ | grep -q "301\|302"; then
        print_success "HTTP重定向测试成功"
    else
        print_warning "HTTP重定向测试失败"
    fi

    # 测试HTTPS访问
    print_info "测试HTTPS访问..."
    if curl -k -s https://localhost/ | grep -q "CentOS Server03"; then
        print_success "HTTPS访问测试成功"
    else
        print_warning "HTTPS访问测试失败"
        # 添加调试信息
        print_info "调试信息: $(curl -k -v https://localhost/ 2>&1)"
    fi

    # 测试SSL证书
    print_info "测试SSL证书..."
    if openssl s_client -connect localhost:443 -servername $SSL_DOMAIN < /dev/null 2>/dev/null | grep -q "Verify return code: 18"; then
        print_success "SSL证书测试成功（自签名证书）"
    else
        print_warning "SSL证书测试异常"
        # 添加调试信息
        print_info "调试信息: $(openssl s_client -connect localhost:443 -servername $SSL_DOMAIN < /dev/null 2>&1)"
    fi

    print_success "SSL服务测试完成"
}

# 添加本地hosts条目
add_hosts_entry() {
    print_header "添加本地hosts条目..."

    if ! grep -q "$SSL_DOMAIN" /etc/hosts; then
        echo "$SERVER_IP $SSL_DOMAIN" >> /etc/hosts
        print_success "已添加hosts条目"
    else
        print_info "hosts条目已存在"
    fi
}

# 主函数
main() {
    print_header "开始配置CentOS Server03 - WEB服务器2 (SSL)..."
    echo ""

    # 检查root权限
    check_root

    # 显示配置信息
    show_config

    # 询问是否添加服务IP地址
    read -p "是否为WEB服务添加IP地址$SERVER_IP/$SUBNET_MASK? (不会影响现有网络配置) (y/n): " answer
    if [[ "$answer" =~ ^[Yy]$ ]]; then
        setup_ip
        echo ""
    fi

    # 配置软件源和安装服务
    configure_centos_repos
    echo ""
    install_apache_ssl
    echo ""
    generate_ssl_certificate
    echo ""
    configure_apache_vhosts
    echo ""
    create_static_website
    echo ""
    configure_firewall
    echo ""
    configure_selinux
    echo ""
    start_apache
    echo ""
    add_hosts_entry
    echo ""
    test_ssl_service

    # 执行最终测试
    print_header "执行最终测试..."
    sleep 3

    # 检查端口监听状态
    print_info "检查端口监听状态..."
    if ss -tulnp | grep -q ":443"; then
        print_success "HTTPS端口443监听正常"
    else
        print_warning "HTTPS端口443监听异常，尝试重启Apache服务"
        systemctl restart httpd
        sleep 2
        if ss -tulnp | grep -q ":443"; then
            print_success "HTTPS端口443监听恢复正常"
        else
            print_error "HTTPS端口443监听仍然异常"
            exit 1
        fi
    fi

    if ss -tulnp | grep -q ":80"; then
        print_success "HTTP端口80监听正常"
    else
        print_warning "HTTP端口80监听异常"
    fi

    # 测试SSL模块
    print_info "检查SSL模块..."
    if httpd -M | grep -q ssl; then
        print_success "SSL模块加载正常"
    else
        print_warning "SSL模块加载异常"
    fi

    echo ""
    print_success "CentOS Server03 - WEB服务器2 (SSL) 配置完成!"
    print_info "服务器地址: $SERVER_IP"
    print_info "SSL域名: $SSL_DOMAIN"
    print_info "访问地址: https://$SSL_DOMAIN 或 https://$SERVER_IP"
    print_info "服务: Apache + SSL"
    print_info "端口: 443 (HTTPS), 80 (重定向到HTTPS)"
    print_info "访问页面:"
    print_info "  - HTTPS主页: https://$SERVER_IP/ (静态网页)"
    print_info "  - HTTP重定向: http://$SERVER_IP/ (自动跳转到HTTPS)"
    print_info "SSL证书: 自签名证书 (浏览器会显示安全警告，这是正常的)"
    print_info "请确保DNS服务器已正确配置$SSL_DOMAIN指向$SERVER_IP"
}

# 执行主函数
main "$@"
